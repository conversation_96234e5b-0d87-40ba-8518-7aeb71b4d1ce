#ifndef __INC_ICPVP_H__
#define __INC_ICPVP_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

/**
 * @brief Pure virtual interface for CPVP class
 * 
 * Provides ABI-stable access to PVP functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on individual PVP combat management.
 */
class ICPVP
{
public:
    virtual ~ICPVP() = default;
    
    // ============================================================================
    // PVP COMBAT OPERATIONS
    // ============================================================================
    
    // Combat results
    virtual void Win(DWORD dwPID) = 0;
    virtual bool CanRevenge(DWORD dwPID) = 0;
    virtual bool IsFight() = 0;
    virtual bool Agree(DWORD dwPID) = 0;
    
    // ============================================================================
    // PVP STATE MANAGEMENT
    // ============================================================================
    
    // Player management
    virtual void SetVID(DWORD dwPID, DWORD dwVID) = 0;
    virtual void Packet(bool bDelete = false) = 0;
    
    // ============================================================================
    // TIMING OPERATIONS
    // ============================================================================
    
    // Fight timing
    virtual void SetLastFightTime() = 0;
    virtual DWORD GetLastFightTime() = 0;
    
    // ============================================================================
    // IDENTIFICATION
    // ============================================================================
    
    // CRC identification
    virtual DWORD GetCRC() = 0;
};

#endif // __INC_ICPVP_H__
