#ifndef __INC_ICDUNGEON_MANAGER_H__
#define __INC_ICDUNGEON_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
class CDungeon;
typedef CDungeon* LPDUNGEON;

#if defined(__DUNGEON_RENEWAL__) || defined(__DEFENSE_WAVE__)
enum EDungeonType;
#endif

/**
 * @brief Pure virtual interface for CDungeonManager singleton
 * 
 * Provides ABI-stable access to dungeon management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on dungeon creation and management.
 */
class IDungeonManager
{
public:
    virtual ~IDungeonManager() = default;
    
    // ============================================================================
    // DUNGEON LIFECYCLE MANAGEMENT
    // ============================================================================
    
    // Dungeon creation
    virtual LPDUNGEON Create(long lOriginalMapIndex
#if defined(__DUNGEON_RENEWAL__) || defined(__DEFENSE_WAVE__)
        , EDungeonType eType = DUNGEON_TYPE_DEFAULT
#endif
    ) = 0;
    
    // Dungeon destruction
    virtual void Destroy(CDungeon::IdType dungeon_id) = 0;
    
    // ============================================================================
    // DUNGEON LOOKUP OPERATIONS
    // ============================================================================
    
    // Find operations
    virtual LPDUNGEON Find(CDungeon::IdType dungeon_id) = 0;
    virtual LPDUNGEON FindByMapIndex(long lMapIndex) = 0;
};

#endif // __INC_ICDUNGEON_MANAGER_H__
