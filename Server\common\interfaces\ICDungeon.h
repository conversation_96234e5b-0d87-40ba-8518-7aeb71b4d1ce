#ifndef __INC_ICDUNGEON_H__
#define __INC_ICDUNGEON_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
class LPREGEN;

/**
 * @brief Pure virtual interface for CDungeon class
 * 
 * Provides ABI-stable access to dungeon functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on individual dungeon instance management.
 */
class ICDungeon
{
public:
    virtual ~ICDungeon() = default;
    
    // ============================================================================
    // DUNGEON IDENTIFICATION
    // ============================================================================
    
    // Dungeon ID
    virtual uint32_t GetId() const = 0;
    
    // ============================================================================
    // DUNGEON NOTIFICATIONS
    // ============================================================================
    
    // Notice system
    virtual void Notice(const char* msg) = 0;
    virtual void MissionMessage(const char* msg) = 0;
    virtual void SubMissionMessage(const char* msg) = 0;
    
    // ============================================================================
    // PARTY MANAGEMENT
    // ============================================================================
    
    // Party operations
    virtual void JoinParty(LPPARTY pParty) = 0;
    virtual void QuitParty(LPPARTY pParty) = 0;
    virtual void Join(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // MEMBER MANAGEMENT
    // ============================================================================
    
    // Member operations
    virtual void IncMember(LPCHARACTER ch) = 0;
    virtual void DecMember(LPCHARACTER ch) = 0;
    virtual void IncPartyMember(LPPARTY pParty, LPCHARACTER ch) = 0;
    virtual void DecPartyMember(LPPARTY pParty, LPCHARACTER ch) = 0;
    
#if defined(__DUNGEON_RENEWAL__)
    // Dungeon destruction
    virtual void Destroy() = 0;
#endif
    
    // ============================================================================
    // MONSTER MANAGEMENT
    // ============================================================================
    
    // Monster operations
    virtual void Purge() = 0;
    virtual void KillAll() = 0;
    virtual void IncMonster() = 0;
    virtual void DecMonster() = 0;
    virtual int CountMonster() = 0;
    virtual int CountRealMonster() = 0;
    
    // ============================================================================
    // KILL TRACKING
    // ============================================================================
    
    // Kill statistics
    virtual void IncKillCount(LPCHARACTER pkKiller, LPCHARACTER pkVictim) = 0;
    virtual int GetKillMobCount() = 0;
    virtual int GetKillStoneCount() = 0;
    virtual bool IsUsePotion() = 0;
    virtual bool IsUseRevive() = 0;
    virtual void UsePotion(LPCHARACTER ch) = 0;
    virtual void UseRevive(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // MAP INFORMATION
    // ============================================================================
    
    // Map operations
    virtual long GetMapIndex() = 0;
    
    // ============================================================================
    // SPAWNING OPERATIONS
    // ============================================================================
    
    // Basic spawning
    virtual void Spawn(DWORD vnum, const char* pos) = 0;
    virtual LPCHARACTER SpawnMob(DWORD vnum, int x, int y, int dir = 0) = 0;
    virtual LPCHARACTER SpawnMob_ac_dir(DWORD vnum, int x, int y, int dir = 0) = 0;
    virtual LPCHARACTER SpawnGroup(DWORD vnum, long x, long y, float radius, bool bAggressive = false, int count = 1) = 0;
    
    // Named spawning
    virtual void SpawnNameMob(DWORD vnum, int x, int y, const char* name) = 0;
    virtual void SpawnGotoMob(long lFromX, long lFromY, long lToX, long lToY) = 0;
    
    // ============================================================================
    // REGEN SYSTEM
    // ============================================================================
    
    // Regen operations
    virtual void SpawnRegen(const char* filename, bool bOnce = true) = 0;
    virtual void AddRegen(LPREGEN regen) = 0;
    virtual void ClearRegen() = 0;
    virtual bool IsValidRegen(LPREGEN regen, size_t regen_id) = 0;
    
    // ============================================================================
    // UNIQUE MOB SYSTEM
    // ============================================================================
    
    // Unique mob management
    virtual void SetUnique(const char* key, DWORD vid) = 0;
    virtual void SpawnMoveUnique(const char* key, DWORD vnum, const char* pos_from, const char* pos_to) = 0;
    virtual void SpawnMoveGroup(DWORD vnum, const char* pos_from, const char* pos_to, int count = 1) = 0;
    virtual void SpawnUnique(const char* key, DWORD vnum, const char* pos) = 0;
    virtual void SpawnStoneDoor(const char* key, const char* pos) = 0;
    virtual void SpawnWoodenDoor(const char* key, const char* pos) = 0;
    
    // Unique mob operations
    virtual void KillUnique(const std::string& key) = 0;
    virtual void PurgeUnique(const std::string& key) = 0;
    virtual bool IsUniqueDead(const std::string& key) = 0;
    virtual float GetUniqueHpPerc(const std::string& key) = 0;
    virtual DWORD GetUniqueVID(const std::string& key) = 0;
    virtual bool IsUnique(LPCHARACTER pChar) = 0;
    virtual LPCHARACTER GetUnique(const std::string& key) = 0;
    
    // ============================================================================
    // CHARACTER DEATH HANDLING
    // ============================================================================
    
    // Death operations
    virtual void DeadCharacter(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // UNIQUE MOB ATTRIBUTES
    // ============================================================================
    
    // Unique mob attributes
    virtual void UniqueSetMaxHP(const std::string& key, DWORD dwMaxHP) = 0;
    virtual void UniqueSetHP(const std::string& key, int iHP) = 0;
    virtual void UniqueSetDefGrade(const std::string& key, int iGrade) = 0;
    
    // ============================================================================
    // PARTY COMMUNICATION
    // ============================================================================
    
    // Party operations
    virtual void SendDestPositionToParty(LPPARTY pParty, long x, long y) = 0;
    
    // ============================================================================
    // DUNGEON STATE MANAGEMENT
    // ============================================================================
    
    // State checking
    virtual void CheckEliminated() = 0;
    
    // ============================================================================
    // MOVEMENT OPERATIONS
    // ============================================================================
    
    // Movement operations
    virtual void JumpAll(long lFromMapIndex, int x, int y) = 0;
    virtual void WarpAll(long lFromMapIndex, int x, int y) = 0;
    virtual void JumpParty(LPPARTY pParty, long lFromMapIndex, int x, int y) = 0;
    
#if defined(__DUNGEON_RENEWAL__)
    // Party management
    virtual void SetParty(LPPARTY pParty) = 0;
    virtual LPPARTY GetParty() const = 0;
#endif
    
    // ============================================================================
    // EXIT OPERATIONS
    // ============================================================================
    
    // Exit operations
    virtual void ExitAll() = 0;
    virtual void ExitAllToStartPosition() = 0;
    virtual void JumpToEliminateLocation() = 0;
    virtual void SetExitAllAtEliminate(long time) = 0;
    virtual void SetWarpAtEliminate(long time, long lMapIndex, int x, int y, const char* regen_file) = 0;
    
    // ============================================================================
    // FLAG SYSTEM
    // ============================================================================
    
    // Flag operations
    virtual int GetFlag(std::string name) = 0;
    virtual void SetFlag(std::string name, int value) = 0;
    virtual void SetWarpLocation(long map_index, int x, int y) = 0;
    
    // ============================================================================
    // ITEM GROUP SYSTEM
    // ============================================================================
    
    // Item group operations
    typedef std::vector <std::pair <DWORD, int>> ItemGroup;
    virtual void CreateItemGroup(std::string& group_name, ItemGroup& item_group) = 0;
    virtual const ItemGroup* GetItemGroup(std::string& group_name) = 0;
    
    // ============================================================================
    // UTILITY OPERATIONS
    // ============================================================================
    
    // Template function for member iteration
    template <class Func> 
    Func ForEachMember(Func f);
    
    // Distance checking
    virtual bool IsAllPCNearTo(int x, int y, int dist) = 0;
    
    // Party management
    virtual void SetPartyNull() = 0;
};

#endif // __INC_ICDUNGEON_H__
