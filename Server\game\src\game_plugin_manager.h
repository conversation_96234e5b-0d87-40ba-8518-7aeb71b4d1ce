#ifndef __INC_GAME_PLUGIN_MANAGER_H__
#define __INC_GAME_PLUGIN_MANAGER_H__

#ifdef WIN32
#include <windows.h>
#endif
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include "../../common/interfaces/IChar.h"
#include "../../common/interfaces/IItem.h"
#include "../../common/interfaces/IGUILD.h"
#include "../../common/interfaces/IShop.h"
#include "../../common/interfaces/ICharManager.h"
#include "../../common/interfaces/IBufferManager.h"
#include "../../common/plugin_manager.h"
#include "../../common/plugin_interface.h"
#include "../../common/plugin_interface_factory.h"
#include "../../common/singleton.h"

#include "typedef.h"  // This already defines LPDESC and other types
#include <functional>

// Forward declarations
class CHARACTER;
class CItem;
class CGuild;
class CShop;
class GamePluginManager;
// Game-specific plugin interface
class IGamePlugin : public IPlugin
{
public:
    virtual ~IGamePlugin() = default;

    // Game event hooks - using ABI-stable interfaces
    virtual void OnCharacterCreate(ICHARACTER* ch) {}
    virtual void OnCharacterDestroy(ICHARACTER* ch) {}
    virtual void OnCharacterLogin(ICHARACTER* ch) {}
    virtual void OnCharacterLogout(ICHARACTER* ch) {}
    virtual void OnCharacterLevelUp(ICHARACTER* ch, BYTE newLevel) {}
    virtual void OnCharacterDead(ICHARACTER* ch, ICHARACTER* killer) {}
    virtual void OnCharacterRevive(ICHARACTER* ch) {}
    
    // Item events - using ABI-stable interfaces
    virtual void OnItemCreate(IITEM* item) {}
    virtual void OnItemDestroy(IITEM* item) {}
    virtual void OnItemEquip(ICHARACTER* ch, IITEM* item) {}
    virtual void OnItemUnequip(ICHARACTER* ch, IITEM* item) {}
    virtual void OnItemUse(ICHARACTER* ch, IITEM* item) {}
    virtual void OnItemDrop(ICHARACTER* ch, IITEM* item) {}
    virtual void OnItemPickup(ICHARACTER* ch, IITEM* item) {}
    
    // Combat events - using ABI-stable interfaces
    virtual void OnAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage) {}
    virtual void OnKill(ICHARACTER* killer, ICHARACTER* victim) {}
    virtual void OnDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage) {}

    // Guild events - using ABI-stable interfaces
    virtual void OnGuildCreate(IGUILD* guild) {}
    virtual void OnGuildDestroy(IGUILD* guild) {}
    virtual void OnGuildJoin(ICHARACTER* ch, IGUILD* guild) {}
    virtual void OnGuildLeave(ICHARACTER* ch, IGUILD* guild) {}
    virtual void OnGuildWar(IGUILD* guild1, IGUILD* guild2) {}
    
    // Shop events - using ABI-stable interfaces
    virtual void OnShopBuy(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) {}
    virtual void OnShopSell(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count) {}

    // Chat events - using ABI-stable interfaces
    virtual void OnChat(ICHARACTER* ch, const char* message, int type) {}
    virtual void OnWhisper(ICHARACTER* from, ICHARACTER* to, const char* message) {}
    virtual void OnShout(ICHARACTER* ch, const char* message) {}

    // Command events - using ABI-stable interfaces
    virtual bool OnCommand(ICHARACTER* ch, const char* command, const char* args) { return false; }

    // Map events - using ABI-stable interfaces
    virtual void OnMapEnter(ICHARACTER* ch, long mapIndex) {}
    virtual void OnMapLeave(ICHARACTER* ch, long mapIndex) {}
    
    // Quest events - using ABI-stable interfaces
    virtual void OnQuestStart(ICHARACTER* ch, int questIndex) {}
    virtual void OnQuestComplete(ICHARACTER* ch, int questIndex) {}
    virtual void OnQuestGiveUp(ICHARACTER* ch, int questIndex) {}
    
    // System events
    virtual void OnServerStart() {}
    virtual void OnServerShutdown() {}
    virtual void OnHeartbeat() {}
    virtual void OnMinuteUpdate() {}
    virtual void OnHourUpdate() {}
    virtual void OnDayUpdate() {}

    // Reigster Game Plugin Manager
	virtual void OnRegisterGamePluginManager(GamePluginManager* manager) {}
	virtual void OnRegisterCharacterManager(ICharacterManager* manager) {}

    // Network packet events
    virtual bool OnPacketReceive(ICHARACTER* ch, BYTE header, const void* data, size_t size) { return false; }
    virtual bool OnPacketSend(ICHARACTER* ch, BYTE header, const void* data, size_t size) { return false; }
};

// Game plugin manager
class GamePluginManager : public PluginManager, public singleton<GamePluginManager>
{
public:
    GamePluginManager();
    virtual ~GamePluginManager();
    
    // Game-specific plugin management
    bool LoadGamePlugin(const std::string& filePath);
    std::shared_ptr<IPlugin> GetGamePlugin(const std::string& pluginName);
    std::vector<IGamePlugin*> GetAllGamePlugins();
	std::vector<std::string> GetAllPluginsNames();

	// Override base class methods
    bool LoadPluginsFromDirectory(const std::string& directory);

    // Plugin registration overrides
    virtual bool RegisterPlugin(IPlugin* plugin) override;
    virtual bool UnregisterPlugin(IPlugin* plugin) override;

    // Plugin system status
    bool IsInitialized() const { return m_bInitialized; }
    
    // Event broadcasting
    void BroadcastCharacterCreate(ICHARACTER* ch);
    void BroadcastCharacterDestroy(ICHARACTER* ch);
    void BroadcastCharacterLogin(ICHARACTER* ch);
    void BroadcastCharacterLogout(ICHARACTER* ch);
    void BroadcastCharacterLevelUp(ICHARACTER* ch, BYTE newLevel);
    void BroadcastCharacterDead(ICHARACTER* ch, ICHARACTER* killer);
    void BroadcastCharacterRevive(ICHARACTER* ch);
    
    void BroadcastItemCreate(IITEM* item);
    void BroadcastItemDestroy(IITEM* item);
    void BroadcastItemEquip(ICHARACTER* ch, IITEM* item);
    void BroadcastItemUnequip(ICHARACTER* ch, IITEM* item);
    void BroadcastItemUse(ICHARACTER* ch, IITEM* item);
    void BroadcastItemDrop(ICHARACTER* ch, IITEM* item);
    void BroadcastItemPickup(ICHARACTER* ch, IITEM* item);
    
    void BroadcastAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage);
    void BroadcastKill(ICHARACTER* killer, ICHARACTER* victim);
    void BroadcastDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage);

    // Combat event aliases (for compatibility)
    void BroadcastCombatAttack(ICHARACTER* attacker, ICHARACTER* victim, int damage);
    void BroadcastCombatDamage(ICHARACTER* victim, ICHARACTER* attacker, int damage);
    void BroadcastCombatKill(ICHARACTER* killer, ICHARACTER* victim);
    
    void BroadcastGuildCreate(IGUILD* guild);
    void BroadcastGuildDestroy(IGUILD* guild);
    void BroadcastGuildJoin(ICHARACTER* ch, IGUILD* guild);
    void BroadcastGuildLeave(ICHARACTER* ch, IGUILD* guild);
    void BroadcastGuildWar(IGUILD* guild1, IGUILD* guild2);
    
    void BroadcastShopBuy(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count);
    void BroadcastShopSell(ICHARACTER* ch, ISHOP* shop, IITEM* item, int count);
    
    void BroadcastChat(ICHARACTER* ch, const char* message, int type);
    void BroadcastWhisper(ICHARACTER* from, ICHARACTER* to, const char* message);
    void BroadcastShout(ICHARACTER* ch, const char* message);
    
    bool BroadcastCommand(ICHARACTER* ch, const char* command, const char* args);
    
    void BroadcastMapEnter(ICHARACTER* ch, long mapIndex);
    void BroadcastMapLeave(ICHARACTER* ch, long mapIndex);
    
    void BroadcastQuestStart(ICHARACTER* ch, int questIndex);
    void BroadcastQuestComplete(ICHARACTER* ch, int questIndex);
    void BroadcastQuestGiveUp(ICHARACTER* ch, int questIndex);
    
    void BroadcastServerStart();
    void BroadcastServerShutdown();
    void BroadcastHeartbeat();
    void BroadcastMinuteUpdate();
    void BroadcastHourUpdate();
    void BroadcastDayUpdate();

    // Network packet broadcasting
    bool BroadcastPacketReceive(ICHARACTER* ch, BYTE header, const void* data, size_t size);
    bool BroadcastPacketSend(ICHARACTER* ch, BYTE header, const void* data, size_t size);
    
    // Interface registration
    void BroadcastRegisterGamePluginManager(GamePluginManager* manager);
    void BroadcastRegisterCharacterManager(ICharacterManager* manager);
    void RegisterCharManager(ICharacterManager* manager);
    void RegisterItemManager(IItemManager* manager);
    void RegisterGuildManager(IGuildManager* manager);
    void RegisterShopManager(IShopManager* manager);
    void RegisterDescManager(IDescManager* manager);
    void RegisterBufferManager(IBufferManager* manager);
    
    // Interface access
    ICharacterManager* GetCharacterManager() const { return m_charManager; }
    IItemManager* GetItemManager() const { return m_itemManager; }
    IGuildManager* GetGuildManager() const { return m_guildManager; }
    IShopManager* GetShopManager() const { return m_shopManager; }
    IDescManager* GetDescManager() const { return m_descManager; }
    IBufferManager* GetBufferManager() const { return m_bufferManager; }
    
    // Plugin event hooks
    virtual void OnPluginLoaded(const std::string& pluginName) override;
    virtual void OnPluginUnloaded(const std::string& pluginName) override;
    virtual void OnPluginError(const std::string& pluginName, const std::string& error) override;
    
protected:
    // Helper methods
    template<typename Func>
    void BroadcastToGamePlugins(Func func);

    bool ValidateGamePlugin(IGamePlugin* plugin);

    // Override plugin loading to use game-specific factory functions
    virtual bool LoadPluginLibrary(const std::string& filePath, PluginHandle& handle) override;
    
private:
    // Interface managers
    ICharacterManager* m_charManager;
    IItemManager* m_itemManager;
    IGuildManager* m_guildManager;
    IShopManager* m_shopManager;
    IDescManager* m_descManager;
    IBufferManager* m_bufferManager;
    
    // Game plugin cache
    std::vector<IGamePlugin*> m_gamePlugins;

    // Event filtering
    std::map<std::string, bool> m_eventFilters;

    // Initialization status
    bool m_bInitialized;
};

// Template implementation
template<typename Func>
void GamePluginManager::BroadcastToGamePlugins(Func func)
{
    for (auto plugin : m_gamePlugins)
    {
        if (plugin && plugin->GetState() == PluginState::PLUGIN_RUNNING)
        {
            try
            {
                func(plugin);
            }
            catch (const std::exception& e)
            {
                OnPluginError(plugin->GetInfo().name, e.what());
            }
        }
    }
}

#endif // __INC_GAME_PLUGIN_MANAGER_H__
