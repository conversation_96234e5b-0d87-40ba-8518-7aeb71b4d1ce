﻿#ifndef __INC_ICHAR_H__
#define __INC_ICHAR_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"
#include "ICFSM.h"
#include "IHorseRider.h"
#include "IEntity.h"
// Forward declarations - use existing types from game
class CItem;
class CGuild;
class CShop;
class CParty;
class CAffect;

// Point types - use existing definitions from tables.h
#ifndef POINT_VALUE
typedef int POINT_VALUE;
#endif
#ifndef POINT_TYPE
typedef BYTE POINT_TYPE;
#endif

class CMob;
class CGuild;
class CWarMap;
class CBuffOnAttributes;
class CPetSystem;
#ifdef ENABLE_GROWTH_PET_SYSTEM
class CItemPet;
#endif
class CExchange;
class CSkillProto;
class CParty;
class CDungeon;
class CWarMap;
class CAffect;
class CGuild;
class CSafebox;
class CArena;
class CShop;
class VID;
typedef class CShop* LPSHOP;
typedef struct SMobSkillInfo TMobSkillInfo;
namespace marriage
{
    class WeddingMap;
}

/**
 * @brief Pure virtual interface for CHARACTER class
 * 
 * Provides ABI-stable access to character functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface allows plugins to interact with characters safely
 * across different compiler boundaries (game server compiled with clang,
 * plugins compiled with gcc).
 */
class ICHARACTER : virtual public IEntity, virtual public IFSM, virtual public IHorseRider
{
protected:
	virtual ~ICHARACTER() = default;
public:
    // ============================================================================
    // BASIC CHARACTER INFORMATION
    // ============================================================================
    
    // Identity
    virtual DWORD			GetPlayerID() const = 0;



    virtual const char* GetName() const = 0;

    virtual void			SetName(const std::string& name) = 0;
    
    // Level and experience
    virtual BYTE GetLevel() const = 0;
    virtual void SetLevel(BYTE bValue) = 0;
    virtual DWORD GetExp() const = 0;
    virtual void SetExp(DWORD dwValue) = 0;
    virtual DWORD GetNextExp() const = 0;
    //
    // // Job and race
    virtual BYTE GetJob() const = 0;
    virtual BYTE GetCharType() const = 0;
    virtual WORD GetRaceNum() const = 0;
    virtual void SetRace(BYTE race) = 0;
    virtual BYTE GetEmpire() const = 0;
    //
    // // Character type checks
    virtual bool IsPC() const = 0;
    virtual bool IsNPC() const = 0;
    virtual bool IsMonster() const = 0;
    virtual bool IsStone() const = 0;
    virtual bool IsDoor() const = 0;
    virtual bool IsBuilding() const = 0;
    virtual bool IsWarp() const = 0;
    virtual bool IsGoto() const = 0;
    virtual bool IsHorse() const = 0;
    
    // // Gender
    virtual bool IsFemale() const = 0;
    virtual bool IsMale() const = 0;
    virtual bool ChangeSex() = 0;
    
    // ============================================================================
    // HEALTH AND STATS
    // ============================================================================
    
    // Health points
    virtual int GetHP() const = 0;
    virtual void SetHP(int val) = 0;
    virtual int GetMaxHP() const = 0;
    virtual void SetMaxHP(int val) = 0;
    virtual int GetHPPct() const = 0;

    // Spirit points
    virtual int GetSP() const = 0;
    virtual void SetSP(int val) = 0;
    virtual int GetMaxSP() const = 0;

    // Stamina
    virtual int GetStamina() const = 0;
    virtual int GetMaxStamina() const = 0;
    
    // Points system
    virtual void SetRealPoint(POINT_TYPE pointType, POINT_VALUE value) = 0;
    virtual POINT_VALUE GetRealPoint(POINT_TYPE pointType) const = 0;
    virtual void SetPoint(POINT_TYPE pointType, POINT_VALUE value) = 0;
    virtual POINT_VALUE GetPoint(POINT_TYPE pointType) const = 0;
    virtual POINT_VALUE GetLimitPoint(POINT_TYPE pointType) const = 0;
    virtual POINT_VALUE GetPolymorphPoint(POINT_TYPE pointType) const = 0;
    //
    // ============================================================================
    // POSITION AND MOVEMENT
    // ============================================================================
    //
    // // Position management

    virtual bool CanMove() const = 0;
    virtual bool Move(long x, long y) = 0;
    virtual void Stop() = 0;
    virtual bool Sync(long x, long y) = 0;
    //
    //
    // Position state
    virtual void SetPosition(int pos) = 0;
    //virtual bool IsPosition(int pos) const = 0;
    //virtual int GetPosition() const = 0;
    //
    // // ============================================================================
    // // COMBAT AND DAMAGE
    // // ============================================================================
    //
    // // Life and death
    virtual bool IsDead() const = 0;
    virtual void Dead(LPCHARACTER killer = nullptr, bool immediate = false) = 0;
    //
    // // Combat state
    virtual bool CanFight() const = 0;
    //
    // // Damage and healing
    virtual bool Damage(LPCHARACTER pAttacker, int dam, EDamageType type = DAMAGE_TYPE_NORMAL) = 0;
    virtual void PointChange(POINT_TYPE wPointType, POINT_VALUE lPointAmount, bool bAmount = false, bool bBroadcast = false) = 0;
    //
    // ============================================================================
    // AFFECTS AND BUFFS
    // ============================================================================
     
     // Affect management
     virtual bool IsAffectFlag(DWORD dwAff) const = 0;
     virtual bool AddAffect(DWORD dwType, POINT_TYPE wApplyOn, POINT_VALUE lApplyValue, DWORD dwFlag, long lDuration, long lSPCost, bool bOverride, bool IsCube = false
#if defined(__AFFECT_RENEWAL__)
                            , bool bRealTime = false
#endif
#if defined(__9TH_SKILL__)
                            , long lValue = 0 /* Skill iAmount2 */
#endif
     ) = 0;
     virtual bool RemoveAffect(DWORD dwType) = 0;
     virtual void RemoveGoodAffect() = 0;
     virtual void RemoveBadAffect() = 0;
//     
//     // ============================================================================
//     // COMMUNICATION
//     // ============================================================================
//     
//     // Chat and messaging
     virtual void ChatPacket(BYTE type, const char* format, ...) = 0;
     
     // ============================================================================
     // SOCIAL SYSTEMS
     // ============================================================================
     
     // Guild
     virtual LPGUILD GetGuild() const = 0;
     virtual void SetGuild(LPGUILD guild) = 0;
     
     // Party
     virtual LPPARTY GetParty() const = 0;
     virtual void SetParty(LPPARTY party) = 0;
     
     // ============================================================================
     // ITEMS AND INVENTORY
     // ============================================================================
     
     // Equipment and inventory
     virtual LPITEM GetWear(WORD wCell) const = 0;
     virtual void SetWear(WORD wCell, LPITEM item) = 0;
     virtual LPITEM GetInventoryItem(WORD cell) const = 0;
     
     // Item operations
    virtual bool CanReceiveItem(LPCHARACTER from, LPITEM item) const = 0;
    virtual void ReceiveItem(LPCHARACTER from, LPITEM item) = 0;
    virtual bool GiveRecallItem(LPITEM item) = 0;
    virtual LPITEM AutoGiveItem(DWORD dwItemVnum, WORD wCount = 1, int iRarePct = -1, bool bMsg = true
#if defined(__WJ_PICKUP_ITEM_EFFECT__)
                        , bool isHighLight = false
#endif
#if defined(__NEW_USER_CARE__)
                        , bool bSystemDrop = true
#endif
    ) = 0;
    virtual void AutoGiveItem(LPITEM item, bool longOwnerShip = false, bool bMsg = true
#if defined(__WJ_PICKUP_ITEM_EFFECT__)
                      , bool isHighLight = false
#endif
    ) = 0;
     // ============================================================================
     // GAME MECHANICS
     // ============================================================================
     
     // Save and persistence
     virtual void Save() = 0;
     virtual void SaveReal() = 0;
     virtual void FlushDelayedSaveItem() = 0;
     
     // Connection management
     virtual void Disconnect(const char* reason = "Disconnected") = 0;
     
     // Updates and packets
     virtual void UpdatePacket() = 0;
     virtual void ComputePoints() = 0;
     
     // GM and admin
     virtual bool IsGM() const = 0;
     virtual BYTE GetGMLevel() const = 0;
     virtual void SetGMLevel() = 0;
     
     // Alignment
     virtual int GetAlignment() const = 0;
     virtual int GetRealAlignment() const = 0;
     virtual void UpdateAlignment(int delta) = 0;
     
     // ============================================================================
     // MOB AND NPC SPECIFIC
     // ============================================================================
     
     // Mob table access
     virtual const TMobTable& GetMobTable() const = 0;
     virtual BYTE GetMobRank() const = 0;
     virtual BYTE GetMobType() const = 0;
     virtual BYTE GetMobBattleType() const = 0;
     virtual BYTE GetMobSize() const = 0;
     virtual DWORD GetMobDamageMin() const = 0;
     virtual DWORD GetMobDamageMax() const = 0;
     virtual WORD GetMobAttackRange() const = 0;
     virtual DWORD GetMobDropItemVnum() const = 0;
     virtual float GetMobDamageMultiply() const = 0;
     
     // AI and behavior
     virtual DWORD GetAIFlag() const = 0;
     virtual void SetAggressive(bool set = true) = 0;
     virtual bool IsAggressive() const = 0;
     
     // ============================================================================
     // PLAYER SPECIFIC
     // ============================================================================
     
     // Player table
     virtual void SetPlayerProto(const TPlayerTable* table) = 0;
     virtual void CreatePlayerProto(TPlayerTable& table) = 0;
     virtual void			SetProto(const CMob* c_pkMob) = 0;

     // Skills
     virtual int GetSkillLevel(DWORD vnum) const = 0;
     virtual void SetSkillLevel(DWORD vnum, BYTE level) = 0;
     virtual bool LearnSkillByBook(DWORD dwSkillVnum, BYTE bProb = 0) = 0;
     
     // Quests
     virtual void SetQuestFlag(const std::string& flag, int value) = 0;
     virtual int GetQuestFlag(const std::string& flag) const = 0;
     
     // Shop
     virtual LPSHOP GetShop() const = 0;
     virtual void SetShop(LPSHOP shop) = 0;
     virtual LPCHARACTER GetShopOwner() const = 0;
     virtual void SetShopOwner(LPCHARACTER owner) = 0;
     
     // Exchange
     virtual bool IsExchanging() const = 0;
     
     // Gold
     virtual int GetGold() const = 0;
     virtual void SetGold(int gold) = 0;
     
     // Parts (appearance)
     virtual void SetPart(BYTE partPos, DWORD value) = 0;
     virtual DWORD GetPart(BYTE partPos) const = 0;
     virtual DWORD GetOriginalPart(BYTE partPos) const = 0;

     virtual DWORD GetMagic() = 0;
};

#endif // __INC_ICHAR_H__
