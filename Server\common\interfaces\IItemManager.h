#ifndef __INC_IITEM_MANAGER_H__
#define __INC_IITEM_MANAGER_H__

#include "../stl.h"
#include "../tables.h"
#include "../../game/src/typedef.h"
/**
 * @brief Pure virtual interface for ITEM_MANAGER singleton
 * 
 * Provides ABI-stable access to item management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on item lifecycle and management operations.
 */
class IItemManager : virtual public Isingleton<IItemManager>
{
public:
    virtual ~IItemManager() = default;
    
    // ============================================================================
    // ITEM CREATION AND DESTRUCTION
    // ============================================================================
    
    // Item creation
    virtual LPITEM CreateItem(DWORD vnum, DWORD count = 1, DWORD dwID = 0, bool bTryMagic = false, int iRarePct = -1, bool bSkipSave = false, bool bSkilAddon = false) = 0;
    virtual void DestroyItem(LPITEM item) = 0;
    virtual void RemoveItem(LPITEM item, const char* c_pszReason = NULL) = 0;

    // ============================================================================
    // ITEM LOOKUP AND MANAGEMENT
    // ============================================================================

    // Find items
    virtual LPITEM Find(DWORD id) = 0;
    virtual LPITEM FindByVID(DWORD vid) = 0;
    
    // Item table access
    virtual bool GetVnum(const char* c_pszName, DWORD& r_dwVnum) = 0;
    virtual bool GetVnumByOriginalName(const char* c_pszName, DWORD& r_dwVnum) = 0;
    virtual TItemTable* GetTable(DWORD vnum) = 0;
    virtual DWORD GetMaskVnum(DWORD dwVnum) = 0;
    

    // ============================================================================
    // SAVE SYSTEM
    // ============================================================================
    
    // Delayed save operations
    virtual void DelayedSave(LPITEM item) = 0;
    virtual void FlushDelayedSave(LPITEM item) = 0;
    virtual void SaveSingleItem(LPITEM item) = 0;



    // ============================================================================
    // ITEM ID MANAGEMENT
    // ============================================================================
    
    // ID range management
    virtual DWORD GetNewID() = 0;
    
    // ============================================================================
    // INITIALIZATION AND CLEANUP
    // ============================================================================
    
    // System management
    virtual bool Initialize(TItemTable* table, int size) = 0;
    virtual void Destroy() = 0;
    virtual void Update() = 0;
    virtual void GracefulShutdown() = 0;
    
    // ============================================================================
    // ITEM DROPS AND REWARDS
    // ============================================================================
    
    // Drop system
    virtual bool CreateDropItem(LPCHARACTER pkChr, LPCHARACTER pkKiller, std::vector<LPITEM>& vec_item) = 0;
    virtual bool CreateDropItemVector(LPCHARACTER ch, LPCHARACTER killer, std::vector<LPITEM>& items) = 0;

};

#endif // __INC_IITEM_MANAGER_H__
