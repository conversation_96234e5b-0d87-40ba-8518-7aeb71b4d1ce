#ifndef __INC_ICARENA_MANAGER_H__
#define __INC_ICARENA_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
struct lua_State;
enum MEMBER_IDENTITY;

/**
 * @brief Pure virtual interface for CArenaManager singleton
 * 
 * Provides ABI-stable access to arena management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on PvP arena system management.
 */
class ICArenaManager
{
public:
    virtual ~ICArenaManager() = default;
    
    // ============================================================================
    // LIFECYCLE MANAGEMENT
    // ============================================================================
    
    // System lifecycle
    virtual void Destroy() = 0;
    
    // ============================================================================
    // ARENA CONFIGURATION
    // ============================================================================
    
    // Arena setup
    virtual bool AddArena(DWORD mapIdx, WORD startA_X, WORD startA_Y, WORD startB_X, WORD startB_Y) = 0;
    
    // ============================================================================
    // ARENA MAP INFORMATION
    // ============================================================================
    
    // Map operations
    virtual void SendArenaMapListTo(LPCHARACTER pChar) = 0;
    virtual bool IsArenaMap(DWORD dwMapIndex) = 0;
    
    // ============================================================================
    // DUEL MANAGEMENT
    // ============================================================================
    
    // Duel operations
    virtual bool StartDuel(LPCHARACTER pCharFrom, LPCHARACTER pCharTo, int nSetPoint, int nMinute = 5) = 0;
    virtual void EndAllDuel() = 0;
    virtual bool EndDuel(DWORD pid) = 0;
    
    // ============================================================================
    // DUEL INFORMATION
    // ============================================================================
    
    // Duel queries
    virtual void GetDuelList(lua_State* L) = 0;
    
    // ============================================================================
    // COMBAT OPERATIONS
    // ============================================================================
    
    // Combat handling
    virtual bool CanAttack(LPCHARACTER pCharAttacker, LPCHARACTER pCharVictim) = 0;
    virtual bool OnDead(LPCHARACTER pCharKiller, LPCHARACTER pCharVictim) = 0;
    
    // ============================================================================
    // OBSERVER SYSTEM
    // ============================================================================
    
    // Observer management
    virtual bool AddObserver(LPCHARACTER pChar, DWORD mapIdx, WORD ObserverX, WORD ObserverY) = 0;
    virtual bool RegisterObserverPtr(LPCHARACTER pChar, DWORD mapIdx, WORD ObserverX, WORD ObserverY) = 0;
    
    // ============================================================================
    // MEMBERSHIP QUERIES
    // ============================================================================
    
    // Member status
    virtual MEMBER_IDENTITY IsMember(DWORD dwMapIndex, DWORD PID) = 0;
};

#endif // __INC_ICARENA_MANAGER_H__
