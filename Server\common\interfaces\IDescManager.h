#ifndef __INC_IDESC_MANAGER_H__
#define __INC_IDESC_MANAGER_H__

#include "../stl.h"
#include "singleton.h"
#include "../../game/src/typedef.h"
typedef std::unordered_set<LPDESC> DESC_SET;


// Forward declarations

/**
 * @brief Pure virtual interface for DESC_MANAGER singleton
 *
 * Provides ABI-stable access to descriptor management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 *
 * This interface focuses on network connection and descriptor management.
 */
class IDescManager : virtual public Isingleton<IDescManager>
{
public:

    virtual ~IDescManager() = default;


	virtual void Initialize() = 0;
	virtual void Destroy() = 0;

	virtual LPDESC AcceptDesc(LPFDWATCH fdw, socket_t s) = 0;
	virtual LPDESC AcceptP2PDesc(LPFDWATCH fdw, socket_t s) = 0;
	virtual void DestroyDesc(LPDESC d, bool erase_from_set = true) = 0;

	virtual DWORD CreateHandshake() = 0;

	virtual LPCLIENT_DESC CreateConnectionDesc(LPFDWATCH fdw, const char* host, WORD port, int iPhaseWhenSucceed, bool bRetryWhenClosed) = 0;
	virtual void TryConnect() = 0;
	virtual LPDESC FindByHandle(DWORD handle) = 0;
	virtual LPDESC FindByHandshake(DWORD dwHandshake) = 0;

	virtual LPDESC FindByCharacterName(const char* name) = 0;
	virtual LPDESC FindByLoginName(const std::string& login) = 0;
	virtual void ConnectAccount(const std::string& login, LPDESC d) = 0;
	virtual void DisconnectAccount(const std::string& login) = 0;

	virtual void DestroyClosed() = 0;

	virtual void UpdateLocalUserCount() = 0;
	virtual DWORD GetLocalUserCount() = 0;
	virtual void GetUserCount(int& iTotal, int** paiEmpireUserCount, int& iLocalCount) = 0;

	virtual const DESC_SET& GetClientSet() = 0;

	virtual DWORD MakeRandomKey(DWORD dwHandle) = 0;
	virtual bool GetRandomKey(DWORD dwHandle, DWORD* prandom_key) = 0;

	virtual DWORD CreateLoginKey(LPDESC d) = 0;
	virtual LPDESC FindByLoginKey(DWORD dwKey) = 0;
	virtual void ProcessExpiredLoginKey() = 0;

	virtual bool IsDisconnectInvalidCRC() = 0;
	virtual void SetDisconnectInvalidCRCMode(bool bMode) = 0;

	virtual bool IsP2PDescExist(const char* szHost, WORD wPort) = 0;

	// for C/S hybrid crypt
	virtual bool LoadClientPackageCryptInfo(const char* pDirName) = 0;
	virtual void SendClientPackageCryptKey(LPDESC desc) = 0;
	virtual void SendClientPackageSDBToLoadMap(LPDESC desc, const char* pMapName) = 0;

};

#endif // __INC_IDESC_MANAGER_H__
