#ifndef __INC_ICWAR_MAP_MANAGER_H__
#define __INC_ICWAR_MAP_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
class CWarMap;
struct TWarMapInfo;
struct TGuildWarInfo;
struct PIXEL_POSITION;

/**
 * @brief Pure virtual interface for CWarMapManager singleton
 * 
 * Provides ABI-stable access to war map management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on guild war map management.
 */
class ICWarMapManager
{
public:
    virtual ~ICWarMapManager() = default;
    
    // ============================================================================
    // INITIALIZATION AND CONFIGURATION
    // ============================================================================
    
    // Configuration loading
    virtual bool LoadWarMapInfo(const char* c_pszFileName) = 0;
    
    // ============================================================================
    // WAR MAP INFORMATION QUERIES
    // ============================================================================
    
    // Map information
    virtual bool IsWarMap(long lMapIndex) = 0;
    virtual TWarMapInfo* GetWarMapInfo(long lMapIndex) = 0;
    virtual bool GetStartPosition(long lMapIndex, BYTE bIdx, PIXEL_POSITION& pos) = 0;
    
    // ============================================================================
    // WAR MAP LIFECYCLE MANAGEMENT
    // ============================================================================
    
    // War map creation and destruction
    virtual long CreateWarMap(const TGuildWarInfo& r_WarInfo, DWORD dwGuildID1, DWORD dwGuildID2) = 0;
    virtual void DestroyWarMap(CWarMap* pMap) = 0;
    
    // ============================================================================
    // WAR MAP LOOKUP AND STATISTICS
    // ============================================================================
    
    // Find operations
    virtual CWarMap* Find(long lMapIndex) = 0;
    virtual int CountWarMap() = 0;
    
    // ============================================================================
    // UTILITY OPERATIONS
    // ============================================================================
    
    // Template function for iteration
    template <typename Func> 
    Func for_each(Func f);
    
    // Shutdown operations
    virtual void OnShutdown() = 0;
};

#endif // __INC_ICWAR_MAP_MANAGER_H__
