#ifndef __INC_ICARENA_H__
#define __INC_ICARENA_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
struct PIXEL_POSITION;

/**
 * @brief Pure virtual interface for CArena class
 * 
 * Provides ABI-stable access to arena functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on individual arena instance management.
 */
class ICArena
{
public:
    virtual ~ICArena() = default;
    
    // ============================================================================
    // PLAYER INFORMATION
    // ============================================================================
    
    // Player ID operations
    virtual DWORD GetPlayerAPID() = 0;
    virtual DWORD GetPlayerBPID() = 0;
    
    // Player character operations
    virtual LPCHARACTER GetPlayerA() = 0;
    virtual LPCHARACTER GetPlayerB() = 0;
    
    // ============================================================================
    // POSITION INFORMATION
    // ============================================================================
    
    // Position operations
    virtual PIXEL_POSITION GetStartPointA() = 0;
    virtual PIXEL_POSITION GetStartPointB() = 0;
    virtual PIXEL_POSITION GetObserverPoint() = 0;
    
    // ============================================================================
    // DUEL MANAGEMENT
    // ============================================================================
    
    // Duel operations
    virtual void EndDuel() = 0;
    virtual void ClearEvent() = 0;
    virtual void OnDisconnect(DWORD pid) = 0;
    
    // ============================================================================
    // OBSERVER SYSTEM
    // ============================================================================
    
    // Observer operations
    virtual void RemoveObserver(DWORD pid) = 0;
    
    // ============================================================================
    // COMMUNICATION
    // ============================================================================
    
    // Communication operations
    virtual void SendPacketToObserver(const void* c_pvData, int iSize) = 0;
    virtual void SendChatPacketToObserver(BYTE type, const char* format, ...) = 0;
};

#endif // __INC_ICARENA_H__
