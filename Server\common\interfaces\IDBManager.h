#ifndef __INC_IDBMANAGER_H__
#define __INC_IDBMANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"

// Forward declarations
class SQLMsg;

/**
 * @brief Pure virtual interface for CDBManager singleton
 * 
 * Provides ABI-stable access to database management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on database connection and query management.
 */
class IDBManager
{
public:
    virtual ~IDBManager() = default;
    
    // ============================================================================
    // DATABASE CONNECTION MANAGEMENT
    // ============================================================================
    
    // Connection lifecycle
    virtual void Initialize() = 0;
    virtual void Destroy() = 0;
    virtual void Clear() = 0;
    virtual void Quit() = 0;
    
    // Connection setup
    virtual int Connect(int iSlot, const char* db_address, const int db_port, const char* db_name, const char* user, const char* pwd) = 0;
    
    // ============================================================================
    // QUERY OPERATIONS
    // ============================================================================
    
    // Query execution
    virtual void ReturnQuery(const char* c_pszQuery, int iType, IDENT dwIdent, void* udata, int iSlot = 0) = 0;
    virtual void AsyncQuery(const char* c_pszQuery, int iSlot = 0) = 0;
    virtual SQLMsg* DirectQuery(const char* c_pszQuery, int iSlot = 0) = 0;
    
    // Result retrieval
    virtual SQLMsg* PopResult() = 0;
    virtual SQLMsg* PopResult(int slot) = 0;
    
    // ============================================================================
    // UTILITY FUNCTIONS
    // ============================================================================
    
    // String operations
    virtual unsigned long EscapeString(void* to, const void* from, unsigned long length, int iSlot = 0) = 0;
    
    // Locale management
    virtual void SetLocale(const char* szLocale) = 0;
};

#endif // __INC_IDBMANAGER_H__
