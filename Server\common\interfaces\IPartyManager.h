#ifndef __INC_IPARTY_MANAGER_H__
#define __INC_IPARTY_MANAGER_H__

#include "../stl.h"
#include "../../game/src/typedef.h"
#include "../singleton.h"
/**
 * @brief Pure virtual interface for PARTY_MANAGER singleton
 * 
 * Provides ABI-stable access to party management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on party lifecycle and management operations.
 */
class IPartyManager : virtual public Isingleton<IPartyManager>
{
public:
    virtual ~IPartyManager() = default;

	virtual void Initialize() = 0;

	//void SendPartyToDB();

	virtual void EnablePCParty() = 0;
	virtual void DisablePCParty() = 0;
	virtual bool IsEnablePCParty() = 0;

	virtual LPPARTY FindParty(DWORD dwPID) = 0;
	virtual LPPARTY CreateParty(LPCHARACTER pkLeader) = 0;
	virtual void DeleteParty(LPPARTY pParty) = 0;
	virtual void DeleteAllParty() = 0;
	virtual bool SetParty(LPCHARACTER pkChr) = 0;

	virtual void SetPartyMember(DWORD dwPID, LPPARTY pParty) = 0;

	virtual void P2PLogin(DWORD pid, const char* name
#if defined(__WJ_SHOW_PARTY_ON_MINIMAP__)
				  , long mapIdx
#endif
#if defined(__PARTY_CHANNEL_FIX__)
				  , BYTE channel
#endif
	) = 0;
	virtual void P2PLogout(DWORD pid) = 0;

	virtual LPPARTY P2PCreateParty(DWORD pid) = 0;
	virtual void P2PDeleteParty(DWORD pid) = 0;
	virtual void P2PJoinParty(DWORD leader, DWORD pid, BYTE role = 0) = 0;
	virtual void P2PQuitParty(DWORD pid) = 0;

};

#endif // __INC_IPARTY_MANAGER_H__
