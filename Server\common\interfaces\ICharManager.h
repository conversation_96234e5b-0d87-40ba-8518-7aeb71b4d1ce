#ifndef __INC_ICHAR_MANAGER_H__
#define __INC_ICHAR_MANAGER_H__

#include "../stl.h"
#include "singleton.h"
#include "../game/src/typedef.h"


/**
 * @brief Pure virtual interface for CHARACTER_MANAGER singleton
 * 
 * Provides ABI-stable access to character management functionality without exposing
 * internal class structure. All methods are pure virtual to ensure
 * stable vtable layout across compilers.
 * 
 * This interface focuses on character lifecycle and management operations.
 */
class ICharacterManager : virtual public Isingleton<ICharacterManager>
{
public:
    
    // ============================================================================
    // CHARACTER LOOKUP
    // ============================================================================
    
    // Find characters by various identifiers
    virtual LPCHARACTER FindByPID(DWORD dwPID) = 0;
    virtual LPCHARACTER FindPC(const char* name) = 0;
    virtual LPCHARACTER Find(DWORD dwVID) = 0;
    
    // ============================================================================
    // CHARACTER CREATION AND MANAGEMENT
    // ============================================================================
    
    // Character lifecycle
    virtual LPCHARACTER CreateCharacter(const char* name, DWORD pid = 0) = 0;
    virtual void DestroyCharacter(LPCHARACTER ch) = 0;
    
    // Spawn management
    virtual LPCHARACTER SpawnMob(DWORD dwVnum, long lMapIndex, long x, long y, long z, bool bSpawnMotion = false, int iRot = -1, bool bShow = true
#if defined(__WJ_SHOW_MOB_INFO__)
        , bool bAggressive = false
#endif
    ) = 0;
    virtual LPCHARACTER SpawnMobRange(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, bool bIsException = false, bool bSpawnMotion = false, bool bAggressive = false) = 0;
    virtual LPCHARACTER SpawnGroup(DWORD dwVnum, long lMapIndex, int sx, int sy, int ex, int ey, LPREGEN pkRegen = NULL, bool bAggressive_ = false, LPDUNGEON pDungeon = NULL) = 0;
    

    // ============================================================================
    // REGISTRATION AND TRACKING
    // ============================================================================
    
    // Race and VID management
    virtual void RegisterRaceNum(DWORD vnum) = 0;
    virtual void RegisterRaceNumMap(LPCHARACTER ch) = 0;
    virtual void UnregisterRaceNumMap(LPCHARACTER ch) = 0;
    
    // Monster logging
    virtual void RegisterForMonsterLog(LPCHARACTER ch) = 0;
    virtual void UnregisterForMonsterLog(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // DELAYED OPERATIONS
    // ============================================================================
    
    // Delayed save system
    virtual void DelayedSave(LPCHARACTER ch) = 0;
    virtual bool FlushDelayedSave(LPCHARACTER ch) = 0;
    virtual void ProcessDelayedSave() = 0;
    
    // ============================================================================
    // STATE MANAGEMENT
    // ============================================================================
    
    // State list management
    virtual bool AddToStateList(LPCHARACTER ch) = 0;
    virtual void RemoveFromStateList(LPCHARACTER ch) = 0;
    
    // ============================================================================
    // GLOBAL OPERATIONS
    // ============================================================================
    

    // Update operations
    virtual void Update(int iPulse) = 0;
    
    // ============================================================================
    // SPECIAL OPERATIONS
    // ============================================================================
    
    // Stone selection
    virtual void SelectStone(LPCHARACTER ch) = 0;
};

#endif // __INC_ICHAR_MANAGER_H__
